// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_places_flutter/google_places_flutter.dart';
import 'package:google_places_flutter/model/prediction.dart';

class LocationResult {
  final double latitude;
  final double longitude;
  final String? name;
  final String? formattedAddress;

  LocationResult({
    required this.latitude,
    required this.longitude,
    this.name,
    this.formattedAddress,
  });

  LatLng get latLng => LatLng(latitude, longitude);
}

class LatLng {
  final double latitude;
  final double longitude;

  LatLng(this.latitude, this.longitude);
}

class VLocationPicker extends StatefulWidget {
  final String googleMapsApiKey;
  final String? languageCode;

  const VLocationPicker({
    super.key,
    required this.googleMapsApiKey,
    this.languageCode,
  });

  @override
  State<VLocationPicker> createState() => _VLocationPickerState();
}

class _VLocationPickerState extends State<VLocationPicker> {
  final TextEditingController _searchController = TextEditingController();
  bool _isLoadingCurrentLocation = false;
  bool _hasLocationPermission = false;
  String? _errorMessage;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      setState(() {
        _hasLocationPermission = permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always;
      });
    } catch (e) {
      setState(() {
        _hasLocationPermission = false;
        _errorMessage = 'Failed to check location permission: $e';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!_hasLocationPermission) {
      await _checkLocationPermission();
      if (!_hasLocationPermission) {
        _showError('Location permission is required');
        return;
      }
    }

    setState(() {
      _isLoadingCurrentLocation = true;
      _errorMessage = null;
    });

    try {
      // Add timeout protection for getting current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 15),
        ),
      ).timeout(
        const Duration(seconds: 20),
        onTimeout: () {
          throw TimeoutException(
              'Location request timed out', const Duration(seconds: 20));
        },
      );

      // Add timeout protection for reverse geocoding
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          // Return empty list if geocoding times out
          return <Placemark>[];
        },
      );

      String? address;
      String? name;

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        name = placemark.name ?? placemark.street;
        address = [
          placemark.street,
          placemark.locality,
          placemark.administrativeArea,
          placemark.country,
        ].where((element) => element != null && element.isNotEmpty).join(', ');
      }

      final result = LocationResult(
        latitude: position.latitude,
        longitude: position.longitude,
        name: name ?? 'Current Location',
        formattedAddress: address ?? 'Current Location',
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } on TimeoutException {
      _showError('Location request timed out. Please try again.');
    } on LocationServiceDisabledException {
      _showError(
          'Location services are disabled. Please enable them in settings.');
    } on PermissionDeniedException {
      _showError(
          'Location permission denied. Please grant permission in settings.');
    } catch (e) {
      _showError('Failed to get current location. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingCurrentLocation = false;
        });
      }
    }
  }

  void _showError(String message) {
    setState(() {
      _errorMessage = message;
    });

    // Auto-hide error after 3 seconds
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _errorMessage = null;
        });
      }
    });
  }

  void _onPlaceSelected(Prediction prediction) async {
    try {
      // Add timeout protection for geocoding
      final locations =
          await locationFromAddress(prediction.description ?? '').timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException(
              'Geocoding request timed out', const Duration(seconds: 10));
        },
      );

      if (locations.isNotEmpty) {
        final location = locations.first;
        final result = LocationResult(
          latitude: location.latitude,
          longitude: location.longitude,
          name: prediction.structuredFormatting?.mainText ??
              prediction.description,
          formattedAddress: prediction.description,
        );

        if (mounted) {
          Navigator.of(context).pop(result);
        }
      } else {
        _showError('No location found for the selected place');
      }
    } on TimeoutException {
      _showError('Location request timed out. Please try again.');
    } catch (e) {
      _showError('Failed to get location details. Please try again.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('Select Location'),
        leading: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Error message
            if (_errorMessage != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CupertinoColors.systemRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: CupertinoColors.systemRed.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: CupertinoColors.systemRed,
                    fontSize: 14,
                  ),
                ),
              ),

            // Current location button
            if (_hasLocationPermission)
              Container(
                margin: const EdgeInsets.all(16),
                child: CupertinoButton.filled(
                  onPressed:
                      _isLoadingCurrentLocation ? null : _getCurrentLocation,
                  child: _isLoadingCurrentLocation
                      ? const CupertinoActivityIndicator(
                          color: CupertinoColors.white)
                      : const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(CupertinoIcons.location,
                                color: CupertinoColors.white),
                            SizedBox(width: 8),
                            Text('Use Current Location'),
                          ],
                        ),
                ),
              ),

            // Search places
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: GooglePlaceAutoCompleteTextField(
                  textEditingController: _searchController,
                  googleAPIKey: widget.googleMapsApiKey,
                  inputDecoration: const InputDecoration(
                    hintText: 'Search for a place...',
                    prefixIcon: Icon(CupertinoIcons.search),
                    border: OutlineInputBorder(),
                  ),
                  debounceTime: 600,
                  countries: const [],
                  isLatLngRequired: true,
                  getPlaceDetailWithLatLng: _onPlaceSelected,
                  itemClick: _onPlaceSelected,
                  seperatedBuilder: const Divider(),
                  containerHorizontalPadding: 0,
                  itemBuilder: (context, index, prediction) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(CupertinoIcons.location_solid),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  prediction.structuredFormatting?.mainText ??
                                      prediction.description ??
                                      '',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (prediction
                                        .structuredFormatting?.secondaryText !=
                                    null)
                                  Text(
                                    prediction
                                        .structuredFormatting!.secondaryText!,
                                    style: const TextStyle(
                                      color: CupertinoColors.systemGrey,
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
